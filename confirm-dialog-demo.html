<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Confirmation Dialog Demo</title>
    
    <!-- Material Design CSS -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/material-components-web/14.0.0/material-components-web.min.css" rel="stylesheet">
    
    <!-- Material Icons -->
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">

    <!-- Custom CSS -->
    <link rel="stylesheet" href="styles.css">
    
    <style>
        body {
            font-family: 'Roboto', sans-serif;
            margin: 0;
            padding: 40px;
            background-color: var(--background-primary);
            color: var(--text-primary);
        }
        
        .demo-container {
            max-width: 800px;
            margin: 0 auto;
        }
        
        .demo-section {
            margin-bottom: 40px;
            padding: 24px;
            background: var(--background-surface);
            border-radius: 12px;
            box-shadow: 0 2px 8px var(--shadow-light);
        }
        
        .demo-title {
            font-size: 24px;
            font-weight: 500;
            margin-bottom: 16px;
            color: var(--primary-color);
        }
        
        .demo-description {
            margin-bottom: 24px;
            color: var(--text-secondary);
            line-height: 1.5;
        }
        
        .demo-buttons {
            display: flex;
            flex-wrap: wrap;
            gap: 16px;
        }
        
        .demo-button {
            min-width: 120px;
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <h1>Confirmation Dialog Demo</h1>
        <p>This page demonstrates the reusable confirmation dialog component with different configurations.</p>
        
        <div class="demo-section">
            <h2 class="demo-title">Basic Confirmations</h2>
            <p class="demo-description">Standard confirmation dialogs with different styles and icons.</p>
            <div class="demo-buttons">
                <button class="mdc-button mdc-button--raised demo-button" id="basic-confirm">
                    <span class="mdc-button__ripple"></span>
                    <span class="mdc-button__label">Basic Confirm</span>
                </button>
                
                <button class="mdc-button mdc-button--raised demo-button" id="delete-confirm" style="background-color: #f44336 !important;">
                    <span class="mdc-button__ripple"></span>
                    <span class="mdc-button__label">Delete Action</span>
                </button>
                
                <button class="mdc-button mdc-button--raised demo-button" id="logout-confirm" style="background-color: #2196f3 !important;">
                    <span class="mdc-button__ripple"></span>
                    <span class="mdc-button__label">Logout</span>
                </button>
            </div>
        </div>
        
        <div class="demo-section">
            <h2 class="demo-title">Different Icon Types</h2>
            <p class="demo-description">Confirmation dialogs with different icon types and colors.</p>
            <div class="demo-buttons">
                <button class="mdc-button mdc-button--outlined demo-button" id="warning-confirm">
                    <span class="mdc-button__ripple"></span>
                    <span class="mdc-button__label">Warning</span>
                </button>
                
                <button class="mdc-button mdc-button--outlined demo-button" id="error-confirm">
                    <span class="mdc-button__ripple"></span>
                    <span class="mdc-button__label">Error</span>
                </button>
                
                <button class="mdc-button mdc-button--outlined demo-button" id="info-confirm">
                    <span class="mdc-button__ripple"></span>
                    <span class="mdc-button__label">Info</span>
                </button>
                
                <button class="mdc-button mdc-button--outlined demo-button" id="success-confirm">
                    <span class="mdc-button__ripple"></span>
                    <span class="mdc-button__label">Success</span>
                </button>
            </div>
        </div>
        
        <div class="demo-section">
            <h2 class="demo-title">Custom Messages</h2>
            <p class="demo-description">Confirmation dialogs with custom titles and messages.</p>
            <div class="demo-buttons">
                <button class="mdc-button mdc-button--outlined demo-button" id="custom-confirm">
                    <span class="mdc-button__ripple"></span>
                    <span class="mdc-button__label">Custom Dialog</span>
                </button>
                
                <button class="mdc-button mdc-button--outlined demo-button" id="long-message-confirm">
                    <span class="mdc-button__ripple"></span>
                    <span class="mdc-button__label">Long Message</span>
                </button>
            </div>
        </div>
    </div>

    <!-- Confirmation Dialog Component -->
    <div class="confirm-dialog-overlay" id="confirm-dialog-overlay">
        <div class="confirm-dialog" id="confirm-dialog">
            <div class="confirm-dialog-icon" id="confirm-dialog-icon">
                <span class="material-icons">warning</span>
            </div>
            <div class="confirm-dialog-content">
                <h3 class="confirm-dialog-title" id="confirm-dialog-title">Confirm Action</h3>
                <p class="confirm-dialog-message" id="confirm-dialog-message">Are you sure you want to proceed?</p>
            </div>
            <div class="confirm-dialog-actions">
                <button type="button" class="mdc-button mdc-button--outlined" id="confirm-dialog-cancel">
                    <span class="mdc-button__ripple"></span>
                    <span class="mdc-button__label">Cancel</span>
                </button>
                <button type="button" class="mdc-button mdc-button--raised confirm-dialog-confirm" id="confirm-dialog-confirm">
                    <span class="mdc-button__ripple"></span>
                    <span class="mdc-button__label">Confirm</span>
                </button>
            </div>
        </div>
    </div>

    <!-- Material Design JavaScript -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/material-components-web/14.0.0/material-components-web.min.js"></script>
    
    <script>
        // Confirmation dialog component instances
        let confirmDialogOverlay;
        let confirmDialog;
        let confirmDialogIcon;
        let confirmDialogTitle;
        let confirmDialogMessage;
        let confirmDialogConfirmBtn;
        let confirmDialogCancelBtn;

        // Initialize confirmation dialog
        function initializeConfirmDialog() {
            confirmDialogOverlay = document.getElementById('confirm-dialog-overlay');
            confirmDialog = document.getElementById('confirm-dialog');
            confirmDialogIcon = document.getElementById('confirm-dialog-icon');
            confirmDialogTitle = document.getElementById('confirm-dialog-title');
            confirmDialogMessage = document.getElementById('confirm-dialog-message');
            confirmDialogConfirmBtn = document.getElementById('confirm-dialog-confirm');
            confirmDialogCancelBtn = document.getElementById('confirm-dialog-cancel');

            // Initialize MDC buttons
            if (window.mdc && window.mdc.ripple) {
                new mdc.ripple.MDCRipple(confirmDialogConfirmBtn);
                new mdc.ripple.MDCRipple(confirmDialogCancelBtn);
            }

            // Add event listeners
            confirmDialogCancelBtn.addEventListener('click', closeConfirmDialog);
            confirmDialogOverlay.addEventListener('click', (e) => {
                if (e.target === confirmDialogOverlay) {
                    closeConfirmDialog();
                }
            });

            // Handle escape key
            document.addEventListener('keydown', (e) => {
                if (e.key === 'Escape' && confirmDialogOverlay.classList.contains('active')) {
                    closeConfirmDialog();
                }
            });
        }

        function showConfirmDialog(config) {
            const {
                title = 'Confirm Action',
                message = 'Are you sure you want to proceed?',
                icon = 'warning',
                iconType = 'warning', // 'warning', 'error', 'info', 'success'
                confirmText = 'Confirm',
                cancelText = 'Cancel',
                confirmStyle = 'primary', // 'primary', 'danger'
                onConfirm = () => {},
                onCancel = () => {}
            } = config;

            // Set dialog content
            confirmDialogTitle.textContent = title;
            confirmDialogMessage.textContent = message;
            confirmDialogConfirmBtn.querySelector('.mdc-button__label').textContent = confirmText;
            confirmDialogCancelBtn.querySelector('.mdc-button__label').textContent = cancelText;

            // Set icon
            confirmDialogIcon.querySelector('.material-icons').textContent = icon;
            
            // Reset icon classes
            confirmDialogIcon.className = 'confirm-dialog-icon';
            if (iconType !== 'warning') {
                confirmDialogIcon.classList.add(iconType);
            }

            // Set confirm button style
            confirmDialogConfirmBtn.className = 'mdc-button mdc-button--raised confirm-dialog-confirm';
            if (confirmStyle === 'danger') {
                confirmDialogConfirmBtn.classList.add('confirm-dialog-confirm--danger');
            }

            // Store handlers
            confirmDialogConfirmBtn.onClickHandler = onConfirm;
            confirmDialogCancelBtn.onClickHandler = onCancel;

            // Add confirm button click handler
            confirmDialogConfirmBtn.onclick = () => {
                if (confirmDialogConfirmBtn.onClickHandler) {
                    confirmDialogConfirmBtn.onClickHandler();
                }
                closeConfirmDialog();
            };

            // Add cancel button click handler
            confirmDialogCancelBtn.onclick = () => {
                if (confirmDialogCancelBtn.onClickHandler) {
                    confirmDialogCancelBtn.onClickHandler();
                }
                closeConfirmDialog();
            };

            // Show dialog
            confirmDialogOverlay.classList.add('active');
            document.body.style.overflow = 'hidden';

            // Focus confirm button
            setTimeout(() => {
                confirmDialogConfirmBtn.focus();
            }, 300);
        }

        function closeConfirmDialog() {
            confirmDialogOverlay.classList.remove('active');
            document.body.style.overflow = '';

            // Clear handlers after animation
            setTimeout(() => {
                confirmDialogConfirmBtn.onClickHandler = null;
                confirmDialogCancelBtn.onClickHandler = null;
                confirmDialogConfirmBtn.onclick = null;
                confirmDialogCancelBtn.onclick = null;
            }, 300);
        }

        // Initialize when DOM is loaded
        document.addEventListener('DOMContentLoaded', () => {
            initializeConfirmDialog();
            
            // Initialize demo buttons
            document.querySelectorAll('.mdc-button').forEach(button => {
                if (window.mdc && window.mdc.ripple) {
                    new mdc.ripple.MDCRipple(button);
                }
            });

            // Demo button event listeners
            document.getElementById('basic-confirm').addEventListener('click', () => {
                showConfirmDialog({
                    title: 'Basic Confirmation',
                    message: 'This is a basic confirmation dialog. Do you want to proceed?',
                    onConfirm: () => alert('Confirmed!'),
                    onCancel: () => alert('Cancelled!')
                });
            });

            document.getElementById('delete-confirm').addEventListener('click', () => {
                showConfirmDialog({
                    title: 'Delete Item',
                    message: 'Are you sure you want to delete this item? This action cannot be undone.',
                    icon: 'delete',
                    iconType: 'error',
                    confirmText: 'Delete',
                    confirmStyle: 'danger',
                    onConfirm: () => alert('Item deleted!'),
                    onCancel: () => alert('Delete cancelled!')
                });
            });

            document.getElementById('logout-confirm').addEventListener('click', () => {
                showConfirmDialog({
                    title: 'Logout',
                    message: 'Are you sure you want to logout?',
                    icon: 'logout',
                    iconType: 'info',
                    confirmText: 'Logout',
                    onConfirm: () => alert('Logged out!'),
                    onCancel: () => alert('Logout cancelled!')
                });
            });

            document.getElementById('warning-confirm').addEventListener('click', () => {
                showConfirmDialog({
                    title: 'Warning',
                    message: 'This action may have consequences. Are you sure?',
                    icon: 'warning',
                    iconType: 'warning',
                    onConfirm: () => alert('Warning confirmed!'),
                    onCancel: () => alert('Warning cancelled!')
                });
            });

            document.getElementById('error-confirm').addEventListener('click', () => {
                showConfirmDialog({
                    title: 'Error Action',
                    message: 'This will perform a potentially destructive action.',
                    icon: 'error',
                    iconType: 'error',
                    confirmText: 'Continue',
                    confirmStyle: 'danger',
                    onConfirm: () => alert('Error action confirmed!'),
                    onCancel: () => alert('Error action cancelled!')
                });
            });

            document.getElementById('info-confirm').addEventListener('click', () => {
                showConfirmDialog({
                    title: 'Information',
                    message: 'This is an informational confirmation dialog.',
                    icon: 'info',
                    iconType: 'info',
                    onConfirm: () => alert('Info confirmed!'),
                    onCancel: () => alert('Info cancelled!')
                });
            });

            document.getElementById('success-confirm').addEventListener('click', () => {
                showConfirmDialog({
                    title: 'Success Action',
                    message: 'This will complete a successful action.',
                    icon: 'check_circle',
                    iconType: 'success',
                    confirmText: 'Complete',
                    onConfirm: () => alert('Success action completed!'),
                    onCancel: () => alert('Success action cancelled!')
                });
            });

            document.getElementById('custom-confirm').addEventListener('click', () => {
                showConfirmDialog({
                    title: 'Custom Dialog Title',
                    message: 'This is a custom confirmation dialog with different button labels.',
                    icon: 'help',
                    iconType: 'info',
                    confirmText: 'Yes, Do It',
                    cancelText: 'No, Cancel',
                    onConfirm: () => alert('Custom action confirmed!'),
                    onCancel: () => alert('Custom action cancelled!')
                });
            });

            document.getElementById('long-message-confirm').addEventListener('click', () => {
                showConfirmDialog({
                    title: 'Long Message Example',
                    message: 'This is an example of a confirmation dialog with a longer message. It demonstrates how the dialog handles multiple lines of text and maintains proper spacing and readability even with more content.',
                    icon: 'description',
                    iconType: 'info',
                    onConfirm: () => alert('Long message confirmed!'),
                    onCancel: () => alert('Long message cancelled!')
                });
            });
        });
    </script>
</body>
</html>

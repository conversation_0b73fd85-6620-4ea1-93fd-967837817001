<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login - Student Management System</title>
    
    <!-- Material Design Components CSS -->
    <link href="https://unpkg.com/material-components-web@latest/dist/material-components-web.min.css" rel="stylesheet">
    
    <!-- Material Icons -->
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="login-styles.css">
</head>
<body>
    <!-- Page Preloader -->
    <div class="page-preloader" id="page-preloader">
        <div class="preloader-content">
            <div class="mdc-circular-progress mdc-circular-progress--large mdc-circular-progress--indeterminate" role="progressbar" aria-label="Loading..." aria-valuemin="0" aria-valuemax="1" id="page-preloader-spinner">
                <div class="mdc-circular-progress__determinate-container">
                    <svg class="mdc-circular-progress__determinate-circle-graphic" viewBox="0 0 48 48" xmlns="http://www.w3.org/2000/svg">
                        <circle class="mdc-circular-progress__determinate-track" cx="24" cy="24" r="18" stroke-width="4"/>
                        <circle class="mdc-circular-progress__determinate-circle" cx="24" cy="24" r="18" stroke-dasharray="113.097" stroke-dashoffset="113.097" stroke-width="4"/>
                    </svg>
                </div>
                <div class="mdc-circular-progress__indeterminate-container">
                    <div class="mdc-circular-progress__spinner-layer">
                        <div class="mdc-circular-progress__circle-clipper mdc-circular-progress__circle-left">
                            <svg class="mdc-circular-progress__indeterminate-circle-graphic" viewBox="0 0 48 48" xmlns="http://www.w3.org/2000/svg">
                                <circle cx="24" cy="24" r="18" stroke-dasharray="113.097" stroke-dashoffset="56.549" stroke-width="4"/>
                            </svg>
                        </div>
                        <div class="mdc-circular-progress__gap-patch">
                            <svg class="mdc-circular-progress__indeterminate-circle-graphic" viewBox="0 0 48 48" xmlns="http://www.w3.org/2000/svg">
                                <circle cx="24" cy="24" r="18" stroke-dasharray="113.097" stroke-dashoffset="56.549" stroke-width="3.2"/>
                            </svg>
                        </div>
                        <div class="mdc-circular-progress__circle-clipper mdc-circular-progress__circle-right">
                            <svg class="mdc-circular-progress__indeterminate-circle-graphic" viewBox="0 0 48 48" xmlns="http://www.w3.org/2000/svg">
                                <circle cx="24" cy="24" r="18" stroke-dasharray="113.097" stroke-dashoffset="56.549" stroke-width="4"/>
                            </svg>
                        </div>
                    </div>
                </div>
            </div>
            <div class="preloader-text">Signing in...</div>
        </div>
    </div>

    <!-- Login Container -->
    <div class="login-container">
        <!-- Background Pattern -->
        <div class="background-pattern"></div>

        <!-- Login Card -->
        <div class="login-card">
            <!-- Left Column - App Information -->
            <div class="login-info-section">
                <div class="info-content">
                    <div class="app-branding">
                        <div class="brand-logo">
                            <span class="material-icons">school</span>
                        </div>
                        <h1 class="brand-title">Student Management System</h1>
                        <p class="brand-subtitle">Streamline your educational administration</p>
                    </div>

                    <div class="feature-highlights">
                        <div class="feature-item">
                            <div class="feature-illustration">
                                <svg viewBox="0 0 400 300" xmlns="http://www.w3.org/2000/svg">
                                    <!-- Student Records Illustration -->
                                    <defs>
                                        <linearGradient id="grad1" x1="0%" y1="0%" x2="100%" y2="100%">
                                            <stop offset="0%" style="stop-color:rgba(255,255,255,0.3);stop-opacity:1" />
                                            <stop offset="100%" style="stop-color:rgba(255,255,255,0.1);stop-opacity:1" />
                                        </linearGradient>
                                    </defs>
                                    <!-- Background -->
                                    <rect x="50" y="50" width="300" height="200" rx="15" fill="url(#grad1)" stroke="rgba(255,255,255,0.4)" stroke-width="2"/>
                                    <!-- Students -->
                                    <circle cx="120" cy="120" r="25" fill="rgba(255,255,255,0.8)"/>
                                    <circle cx="200" cy="120" r="25" fill="rgba(255,255,255,0.8)"/>
                                    <circle cx="280" cy="120" r="25" fill="rgba(255,255,255,0.8)"/>
                                    <!-- Bodies -->
                                    <rect x="105" y="140" width="30" height="40" rx="15" fill="rgba(255,255,255,0.6)"/>
                                    <rect x="185" y="140" width="30" height="40" rx="15" fill="rgba(255,255,255,0.6)"/>
                                    <rect x="265" y="140" width="30" height="40" rx="15" fill="rgba(255,255,255,0.6)"/>
                                    <!-- Document lines -->
                                    <line x1="70" y1="200" x2="150" y2="200" stroke="rgba(255,255,255,0.6)" stroke-width="2"/>
                                    <line x1="70" y1="210" x2="130" y2="210" stroke="rgba(255,255,255,0.4)" stroke-width="2"/>
                                    <line x1="70" y1="220" x2="140" y2="220" stroke="rgba(255,255,255,0.4)" stroke-width="2"/>
                                </svg>
                            </div>
                            <div class="feature-text">
                                <h3>Student Records</h3>
                                <p>Comprehensive student information management</p>
                            </div>
                        </div>

                        <div class="feature-item">
                            <div class="feature-illustration">
                                <svg viewBox="0 0 400 300" xmlns="http://www.w3.org/2000/svg">
                                    <!-- Analytics Illustration -->
                                    <defs>
                                        <linearGradient id="grad2" x1="0%" y1="0%" x2="100%" y2="100%">
                                            <stop offset="0%" style="stop-color:rgba(255,255,255,0.3);stop-opacity:1" />
                                            <stop offset="100%" style="stop-color:rgba(255,255,255,0.1);stop-opacity:1" />
                                        </linearGradient>
                                    </defs>
                                    <!-- Chart background -->
                                    <rect x="50" y="50" width="300" height="200" rx="15" fill="url(#grad2)" stroke="rgba(255,255,255,0.4)" stroke-width="2"/>
                                    <!-- Bar chart -->
                                    <rect x="80" y="180" width="30" height="50" fill="rgba(255,255,255,0.8)"/>
                                    <rect x="130" y="160" width="30" height="70" fill="rgba(255,255,255,0.8)"/>
                                    <rect x="180" y="140" width="30" height="90" fill="rgba(255,255,255,0.8)"/>
                                    <rect x="230" y="170" width="30" height="60" fill="rgba(255,255,255,0.8)"/>
                                    <rect x="280" y="150" width="30" height="80" fill="rgba(255,255,255,0.8)"/>
                                    <!-- Trend line -->
                                    <polyline points="95,200 145,180 195,160 245,185 295,170" stroke="rgba(255,255,255,0.9)" stroke-width="3" fill="none"/>
                                    <!-- Data points -->
                                    <circle cx="95" cy="200" r="4" fill="rgba(255,255,255,1)"/>
                                    <circle cx="145" cy="180" r="4" fill="rgba(255,255,255,1)"/>
                                    <circle cx="195" cy="160" r="4" fill="rgba(255,255,255,1)"/>
                                    <circle cx="245" cy="185" r="4" fill="rgba(255,255,255,1)"/>
                                    <circle cx="295" cy="170" r="4" fill="rgba(255,255,255,1)"/>
                                </svg>
                            </div>
                            <div class="feature-text">
                                <h3>Analytics & Reports</h3>
                                <p>Detailed insights and performance tracking</p>
                            </div>
                        </div>

                        <div class="feature-item">
                            <div class="feature-illustration">
                                <svg viewBox="0 0 400 300" xmlns="http://www.w3.org/2000/svg">
                                    <!-- Security Illustration -->
                                    <defs>
                                        <linearGradient id="grad3" x1="0%" y1="0%" x2="100%" y2="100%">
                                            <stop offset="0%" style="stop-color:rgba(255,255,255,0.3);stop-opacity:1" />
                                            <stop offset="100%" style="stop-color:rgba(255,255,255,0.1);stop-opacity:1" />
                                        </linearGradient>
                                    </defs>
                                    <!-- Shield -->
                                    <path d="M200 60 L160 80 L160 180 Q160 220 200 240 Q240 220 240 180 L240 80 Z" fill="url(#grad3)" stroke="rgba(255,255,255,0.6)" stroke-width="3"/>
                                    <!-- Lock -->
                                    <rect x="180" y="140" width="40" height="30" rx="5" fill="rgba(255,255,255,0.8)"/>
                                    <path d="M185 140 L185 125 Q185 115 195 115 Q205 115 205 125 L205 140" stroke="rgba(255,255,255,0.8)" stroke-width="3" fill="none"/>
                                    <!-- Keyhole -->
                                    <circle cx="200" cy="150" r="3" fill="rgba(66,165,245,0.8)"/>
                                    <rect x="198" y="153" width="4" height="8" fill="rgba(66,165,245,0.8)"/>
                                    <!-- Security dots -->
                                    <circle cx="170" cy="100" r="3" fill="rgba(255,255,255,0.6)"/>
                                    <circle cx="230" cy="110" r="3" fill="rgba(255,255,255,0.6)"/>
                                    <circle cx="180" cy="200" r="3" fill="rgba(255,255,255,0.6)"/>
                                    <circle cx="220" cy="190" r="3" fill="rgba(255,255,255,0.6)"/>
                                </svg>
                            </div>
                            <div class="feature-text">
                                <h3>Secure & Reliable</h3>
                                <p>Enterprise-grade security for your data</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Right Column - Login Form -->
            <div class="login-form-section">
                <div class="form-content">
                    <div class="form-header">
                        <h2 class="form-title">Welcome Back</h2>
                        <p class="form-subtitle">Please sign in to your account</p>
                    </div>

            <form class="login-form" id="login-form">
                <!-- School Year Field -->
                <div class="form-field" style="z-index: 50">
                    <div class="mdc-select mdc-select--outlined mdc-select--with-leading-icon">
                        <div class="mdc-select__anchor" role="button" aria-haspopup="listbox" aria-expanded="false">
                            <span class="mdc-select__icon material-icons">event</span>
                            <span class="mdc-select__selected-text-container">
                                <span class="mdc-select__selected-text"></span>
                            </span>
                            <span class="mdc-select__dropdown-icon">
                                <svg class="mdc-select__dropdown-icon-graphic" viewBox="7 10 10 5" focusable="false">
                                    <polygon class="mdc-select__dropdown-icon-inactive" stroke="none" fill-rule="evenodd" points="7 10 12 15 17 10"></polygon>
                                    <polygon class="mdc-select__dropdown-icon-active" stroke="none" fill-rule="evenodd" points="7 15 12 10 17 15"></polygon>
                                </svg>
                            </span>
                            <div class="mdc-notched-outline">
                                <div class="mdc-notched-outline__leading"></div>
                                <div class="mdc-notched-outline__notch">
                                    <label class="mdc-floating-label">School Year</label>
                                </div>
                                <div class="mdc-notched-outline__trailing"></div>
                            </div>
                        </div>
                        <div class="mdc-select__menu mdc-menu mdc-menu-surface mdc-menu-surface--fullwidth">
                            <ul class="mdc-deprecated-list" role="listbox">
                                <li class="mdc-deprecated-list-item mdc-deprecated-list-item--selected" data-value="2024-2025" role="option" aria-selected="true">
                                    <span class="mdc-deprecated-list-item__ripple"></span>
                                    <span class="mdc-deprecated-list-item__text">2024-2025</span>
                                </li>
                                <li class="mdc-deprecated-list-item" data-value="2023-2024" role="option">
                                    <span class="mdc-deprecated-list-item__ripple"></span>
                                    <span class="mdc-deprecated-list-item__text">2023-2024</span>
                                </li>
                                <li class="mdc-deprecated-list-item" data-value="2022-2023" role="option">
                                    <span class="mdc-deprecated-list-item__ripple"></span>
                                    <span class="mdc-deprecated-list-item__text">2022-2023</span>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>

                <!-- Username Field -->
                <div class="form-field">
                    <div class="mdc-text-field mdc-text-field--outlined mdc-text-field--with-leading-icon">
                        <span class="mdc-text-field__icon mdc-text-field__icon--leading material-icons">person</span>
                        <input type="text" class="mdc-text-field__input" id="username" required>
                        <div class="mdc-notched-outline">
                            <div class="mdc-notched-outline__leading"></div>
                            <div class="mdc-notched-outline__notch">
                                <label for="username" class="mdc-floating-label">Username</label>
                            </div>
                            <div class="mdc-notched-outline__trailing"></div>
                        </div>
                    </div>
                </div>

                <!-- Password Field -->
                <div class="form-field">
                    <div class="mdc-text-field mdc-text-field--outlined mdc-text-field--with-leading-icon mdc-text-field--with-trailing-icon">
                        <span class="mdc-text-field__icon mdc-text-field__icon--leading material-icons">lock</span>
                        <input type="password" class="mdc-text-field__input" id="password" required>
                        <button type="button" class="mdc-icon-button mdc-text-field__icon mdc-text-field__icon--trailing" id="toggle-password">
                            <span class="material-icons">visibility</span>
                        </button>
                        <div class="mdc-notched-outline">
                            <div class="mdc-notched-outline__leading"></div>
                            <div class="mdc-notched-outline__notch">
                                <label for="password" class="mdc-floating-label">Password</label>
                            </div>
                            <div class="mdc-notched-outline__trailing"></div>
                        </div>
                    </div>
                </div>

                <!-- Remember Me Checkbox -->
                <div class="form-field checkbox-field">
                    <div class="mdc-form-field">
                        <div class="mdc-checkbox">
                            <input type="checkbox" class="mdc-checkbox__native-control" id="remember-me">
                            <div class="mdc-checkbox__background">
                                <svg class="mdc-checkbox__checkmark" viewBox="0 0 24 24">
                                    <path class="mdc-checkbox__checkmark-path" fill="none" d="M1.73,12.91 8.1,19.28 22.79,4.59"/>
                                </svg>
                                <div class="mdc-checkbox__mixedmark"></div>
                            </div>
                            <div class="mdc-checkbox__ripple"></div>
                        </div>
                        <label for="remember-me">Remember me</label>
                    </div>
                </div>

                <!-- Login Button -->
                <div class="form-field">
                    <button type="submit" class="mdc-button mdc-button--raised login-button" id="login-button">
                        <span class="mdc-button__ripple"></span>
                        <span class="material-icons mdc-button__icon">login</span>
                        <span class="mdc-button__label">Sign In</span>
                    </button>
                </div>

                    <!-- Forgot Password Link -->
                    <div class="form-field">
                        <a href="#" class="forgot-password-link">Forgot your password?</a>
                    </div>
                </form>

                <!-- Footer -->
                <div class="form-footer">
                    <p>&copy; 2024 Student Management System. All rights reserved.</p>
                </div>
            </div>
        </div>
    </div>
    </div>

    <!-- Material Design Components JS -->
    <script src="https://unpkg.com/material-components-web@latest/dist/material-components-web.min.js"></script>
    
    <!-- Custom JS -->
    <script src="login-script.js"></script>
</body>
</html>
